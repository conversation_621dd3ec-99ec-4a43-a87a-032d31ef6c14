<script setup lang="ts">
import {onMounted, ref} from "vue";
import axios from "axios";
import qs from "qs";
import AddProfessor from "./components/AddProfessor.vue";

type Scoala = {
  id: number;
  nume: string;
  localitate: string;
  nivel: string;
};

type Professor = {
  id: number;
  nume: string;
  materie: string;
  email: string;
  schoolId: number;
  school: Scoala;
};

const scoli = ref([]);
const profesori = ref<Professor[]>([]);

const filtre = ref({
  nume: "Ionut Popescu",
  materie: undefined,
  email: undefined,
  scoala: {
    id: undefined,
    nume: undefined,
    localitate: undefined,
    nivel: undefined,
  },
});

const filtreaza = async () => {
  const params = qs.stringify(filtre.value, {
    allowDots: true,
  })
  const URL = `/professors?${params}&_expand=school`;
  axios
    .get(URL)
    .then((response) => {
      console.log(response.data);
      profesori.value = response.data;
    });
}

const iaScoli = async () => {
  await axios
    .get("/schools")
    .then((response) => {
      scoli.value = response.data;
    });
}

onMounted(async () => {
  await filtreaza();
  await iaScoli();
})
</script>

<template>
  <main>
    <AddProfessor :scoli="scoli"/>
    <table>
      <thead>
        <tr>
          <th>Nume</th>
          <th>Email</th>
          <th>Materie</th>
          <th>Scoala</th>
          <th>Localitate</th>
          <th>Nivel</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="profesor in profesori" :key="profesor.id">
          <td>{{ profesor.nume }}</td>
          <td>{{ profesor.email }}</td>
          <td>{{ profesor.materie }}</td>
          <td>{{ profesor.school.nume }}</td>
          <td>{{ profesor.school.localitate }}</td>
          <td>{{ profesor.school.nivel }}</td>
        </tr>
      </tbody>
    </table>
  </main>
</template>

<style>
table {
  width: 100%;
  min-width: 1200px;
  border-collapse: collapse;
  border: 1px solid #ccc;
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
}

form {
  border: 1px solid #ccc;
  padding: 0.5rem;
  margin-bottom: 1rem;
}
</style>
