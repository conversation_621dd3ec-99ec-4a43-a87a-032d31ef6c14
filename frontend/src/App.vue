<script setup lang="ts">
import {onMounted, ref} from "vue";
import axios from "axios";
import qs from "qs";
import AddProfessor from "./components/AddProfessor.vue";
import Filtre from "./components/Filtre.vue";

type Scoala = {
  id: number;
  nume: string;
  localitate: string;
  nivel: string;
};

type Professor = {
  id: number;
  nume: string;
  materie: string;
  email: string;
  schoolId: number;
  school: Scoala;
};

const scoli = ref([]);
const profesori = ref<Professor[]>([]);

const filtre = ref({
  nume: undefined,
  materie: undefined,
  email: undefined,
});

const filtreaza = async () => {
  const filtreActualizate = Object.entries(filtre.value).reduce((acc, [key, value]) => {
    if (value) {
      acc[`${key}_like`] = value;
    }
    return acc;
  }, {});
  const params = qs.stringify(filtre.value, {
    allowDots: true,
  })
  const URL = `/professors?${params}&_expand=school`;
  axios
      .get(URL)
      .then((response) => {
        console.log(response.data);
        profesori.value = response.data;
      });
}

const iaScoli = async () => {
  await axios
      .get("/schools")
      .then((response) => {
        scoli.value = response.data;
      });
}

onMounted(async () => {
  await filtreaza();
  await iaScoli();
})
</script>

<template>
  <main>
    <AddProfessor :scoli="scoli" @add-professor="filtreaza"/>
    <Filtre v-model="filtre" @filter-change="filtreaza"/>
    <table>
      <thead>
      <tr>
        <th>Nume</th>
        <th>Email</th>
        <th>Materie</th>
        <th>Scoala</th>
        <th>Localitate</th>
        <th>Nivel</th>
      </tr>
      </thead>
      <tbody>
      <tr v-for="profesor in profesori" :key="profesor.id">
        <td>{{ profesor.nume }}</td>
        <td>{{ profesor.email }}</td>
        <td>{{ profesor.materie }}</td>
        <td>{{ profesor.school.nume }}</td>
        <td>{{ profesor.school.localitate }}</td>
        <td>{{ profesor.school.nivel }}</td>
      </tr>
      </tbody>
    </table>
  </main>
</template>

<style>
table {
  width: 100%;
  min-width: 1200px;
  border-collapse: collapse;
  border: 1px solid #ccc;
  margin-top: 1rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
}

form {
  border: 1px solid #ccc;
  padding: 0.5rem;
  margin-bottom: 1rem;
}
</style>
