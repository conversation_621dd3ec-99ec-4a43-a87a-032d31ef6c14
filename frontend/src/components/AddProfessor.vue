<script setup lang="ts">
import {ref} from "vue";
import axios from "axios";

const props = defineProps<{
  scoli: {id: number, nume: string}[];
}>();

const emits = defineEmits<{
  (e: "add-professor"): void;
}>();

const valori = ref({
  nume: undefined,
  materie: undefined,
  email: undefined,
  schoolId: undefined,
});

const onSubmit = () => {
  axios.post("/professors", valori.value)
      .then((response) => {
        console.log(response.data);
        emits("add-professor")
      })
      .catch((error) => {
        console.error(error);
      });
};
</script>

<template>
  <form class="add-professor-form" @submit.prevent="onSubmit">
    <span>
      <label for="nume">Nume</label>
      <input type="text" id="nume" placeholder="Ionut Popescu" required v-model="valori.nume"/>
    </span>
    <span>
      <label for="materie">Materie</label>
      <input type="text" id="materie" placeholder="Matematica" required v-model="valori.materie"/>
    </span>
    <span>
      <label for="email">Email</label>
      <input type="email" id="email" placeholder="<EMAIL>" required v-model="valori.email"/>
    </span>
    <span>
      <label for="schoolId">Scoala</label>
      <select id="schoolId" required v-model="valori.schoolId">
        <option v-for="scoala in props.scoli" :value="scoala.id">
          {{ scoala.nume }}
        </option>
      </select>
    </span>
    <button type="submit">Adauga</button>
  </form>
</template>

<style scoped>
.add-professor-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

label {
  display: block;
  text-align: left;
}

select, input {
  width: 100%;
}
</style>