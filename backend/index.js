import express from "express";
import axios from "axios";

axios.defaults.baseURL = "http://localhost:4000";

const app = express();

app.use(express.json());

app.use((req, res, next) => {
  res.header("Access-Control-Allow-Origin", "*");
  res.header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization");
  res.header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");

  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  next();
});

app.get("/professors", async (req, res) => {
  try {
    const response = await axios.get(req.originalUrl);
    res.send(response.data);
  } catch (error) {
    console.error(error);
    res.status(500).send([]);
  }
});

app.post("/professors", async (req, res) => {
  const profesor = req.body;
  res.send(profesor);
});

const PORT = 8088;

app.listen(PORT, () => {
  console.log(`Server started on port ${PORT}`);
});
